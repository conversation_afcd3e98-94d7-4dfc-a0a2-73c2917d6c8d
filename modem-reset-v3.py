#!/usr/bin/env python3

import requests
import hashlib
import time
import re
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for self-signed certificates
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Configuration
MODEM_URL = "https://***********"  # Use HTTPS like our working script
GOFORM_GET_URL = f"{MODEM_URL}/goform/goform_get_cmd_process"
GOFORM_SET_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "admin"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest().upper()

def calculate_password_hash_v3(password, ld_token):
    """Calculate password hash using bash script method: SHA256(SHA256(password) + LD)"""
    pwd_hash = get_sha256(password)
    return get_sha256(pwd_hash + ld_token)

def calculate_ad_hash_v3(wa_inner_version, cr_version, rd_token):
    """Calculate AD hash using bash script method: SHA256(SHA256(wa_inner_version + cr_version) + RD)"""
    # Note: bash script uses wa_inner_version + cr_version (reversed order!)
    a = get_sha256(wa_inner_version + cr_version)
    return get_sha256(a + rd_token)

def generate_ad(session, wa_inner_version, cr_version):
    """Generate fresh AD hash like bash script's generateAD function"""
    timestamp = int(time.time() * 1000)
    params = {
        "isTest": "false",
        "cmd": "RD",
        "_": str(timestamp)
    }
    response = session.get(GOFORM_GET_URL, params=params)
    response.raise_for_status()

    rd_data = response.json()
    rd_token = rd_data.get("RD", "")
    print(f"Got fresh RD token: {rd_token}")

    ad_hash = calculate_ad_hash_v3(wa_inner_version, cr_version, rd_token)
    print(f"Generated AD hash: {ad_hash}")
    return ad_hash

def main():
    print("🚀 ZTE Modem Reset Script v3 (Based on Working Bash Script)")
    print("=" * 65)

    # Create session with proper headers (matching bash script)
    session = requests.Session()
    session.verify = False  # Disable SSL verification like the warning disable above
    session.headers.update({
        "Referer": f"{MODEM_URL}/",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })

    try:
        # 1. Get LD token
        print("Reading LD...")
        timestamp = int(time.time() * 1000)
        params = {
            "isTest": "false",
            "cmd": "LD",
            "_": str(timestamp)
        }
        response = session.get(GOFORM_GET_URL, params=params)
        response.raise_for_status()

        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")

        ld_data = response.json()
        ld_token = ld_data.get("LD", "")
        print(f"LD={ld_token}")

        # 2. Get language info (device versions)
        print("\nReading Language Info...")
        timestamp = int(time.time() * 1000)
        params = {
            "isTest": "false",
            "cmd": "Language,cr_version,wa_inner_version",
            "multi_data": "1",
            "_": str(timestamp)
        }
        response = session.get(GOFORM_GET_URL, params=params)
        response.raise_for_status()

        version_data = response.json()
        cr_version = version_data.get("cr_version", "")
        wa_inner_version = version_data.get("wa_inner_version", "")
        print(f"Language info: {version_data}")

        # Calculate 'a' value (like bash script)
        a = get_sha256(wa_inner_version + cr_version)  # Note: reversed order!
        print(f"a={a}")

        # 3. Calculate password hash using bash script method
        pwd_hash = get_sha256(PASSWORD)
        url_pwd = get_sha256(pwd_hash + ld_token)
        print(f"Password hash: {url_pwd}")

        # 4. Get RD token for login
        print("Getting RD token for login...")
        timestamp = int(time.time() * 1000)
        params = {
            "isTest": "false",
            "cmd": "RD",
            "_": str(timestamp)
        }
        response = session.get(GOFORM_GET_URL, params=params)
        response.raise_for_status()

        rd_data = response.json()
        rd_token = rd_data.get("RD", "")
        print(f"RD={rd_token}")

        # Calculate AD hash using bash script method (reversed order)
        ad_hash = calculate_ad_hash_v3(wa_inner_version, cr_version, rd_token)
        print(f"AD hash: {ad_hash}")

        # 5. Login using LOGIN_MULTI_USER (which works) with bash script hash methods
        print("\nLOGIN (hybrid method)")
        login_data = {
            "isTest": "false",
            "goformId": "LOGIN_MULTI_USER",
            "user": USERNAME,
            "password": url_pwd,
            "AD": ad_hash
        }

        response = session.post(GOFORM_GET_URL, data=login_data)
        response.raise_for_status()

        login_result = response.json()
        print(f"Login response: {login_result}")

        # Extract zsidn cookie from response
        zsidn_cookie = None
        for cookie in session.cookies:
            if cookie.name == 'zsidn':
                zsidn_cookie = cookie.value
                print(f"Got zsidn cookie: {zsidn_cookie}")
                break

        if not zsidn_cookie:
            print("❌ No zsidn cookie received")
            return

        # Add cookie to session headers
        session.headers["Cookie"] = f'zsidn="{zsidn_cookie}"'
        print(f"Cookie: zsidn=\"{zsidn_cookie}\"")

        # 5. Disconnect network
        print("\nDisconnecting network...")
        ad_hash = generate_ad(session, wa_inner_version, cr_version)

        disconnect_data = {
            "isTest": "false",
            "goformId": "DISCONNECT_NETWORK",
            "notCallback": "true",
            "AD": ad_hash
        }

        response = session.post(GOFORM_SET_URL, data=disconnect_data)
        response.raise_for_status()
        disconnect_result = response.json()
        print(f"Disconnect response: {disconnect_result}")

        # 6. Wait and reconnect
        time.sleep(3)
        print("\nReconnecting network...")
        ad_hash = generate_ad(session, wa_inner_version, cr_version)

        connect_data = {
            "isTest": "false",
            "goformId": "CONNECT_NETWORK",
            "notCallback": "true",
            "AD": ad_hash
        }

        response = session.post(GOFORM_SET_URL, data=connect_data)
        response.raise_for_status()
        connect_result = response.json()
        print(f"Connect response: {connect_result}")

        # 7. Logout (like bash script)
        print("\nLOGOUT")
        ad_hash = generate_ad(session, wa_inner_version, cr_version)

        logout_data = {
            "isTest": "false",
            "goformId": "LOGOUT",
            "AD": ad_hash
        }

        response = session.post(GOFORM_SET_URL, data=logout_data)
        response.raise_for_status()
        logout_result = response.json()
        print(f"Logout response: {logout_result}")

        # Check results
        disconnect_success = disconnect_result.get("result") == "success"
        connect_success = connect_result.get("result") == "success"

        if disconnect_success and connect_success:
            print("\n✅ IP change process completed successfully!")
        else:
            print(f"\n⚠️  Results - Disconnect: {disconnect_result.get('result')}, Connect: {connect_result.get('result')}")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
