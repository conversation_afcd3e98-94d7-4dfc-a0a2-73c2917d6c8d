import requests
import hashlib
import re

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://192.168.1.1"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "superonline"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest()

def get_md5(input_string):
    """Returns the MD5 hash of a string."""
    return hashlib.md5(input_string.encode()).hexdigest()

def calculate_password_hash(password, ld):
    """Calculate password hash as per ZTE modem specification."""
    prefix_hash = get_sha256(password).upper()
    return get_sha256(prefix_hash + ld.upper()).upper()

def calculate_ad_hash(cr_version, wa_inner_version, rd):
    """Calculate AD hash as per ZTE modem specification."""
    prefix_hash = get_md5(cr_version + wa_inner_version)
    return get_md5(prefix_hash + rd).upper()

def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    try:
        # 1. Get device version info (cr_version and wa_inner_version)
        print("Getting device version info...")
        import time
        timestamp = int(time.time() * 1000)
        version_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=Language%2Ccr_version%2Cwa_inner_version&multi_data=1&_={timestamp}"
        print(f"Version URL: {version_url}")
        response = session.get(version_url)
        response.raise_for_status()

        version_data = response.json()
        print(f"Version response: {version_data}")
        cr_version = version_data.get("cr_version", "")
        wa_inner_version = version_data.get("wa_inner_version", "")
        print(f"Got version info - cr_version: '{cr_version}', wa_inner_version: '{wa_inner_version}'")

        # 2. Get LD token
        print("Getting LD token...")
        timestamp += 1
        ld_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=LD&_={timestamp}"
        response = session.get(ld_url)
        response.raise_for_status()

        ld_data = response.json()
        if "LD" not in ld_data:
            print(f"Could not get LD token. Response: {response.text}")
            return

        ld_token = ld_data["LD"]
        print(f"Got LD token: {ld_token}")

        # 3. Get RD token
        print("Getting RD token...")
        timestamp += 1
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        if "RD" not in rd_data:
            print(f"Could not get RD token. Response: {response.text}")
            return

        rd_token = rd_data["RD"]
        print(f"Got RD token: {rd_token}")

        # 4. Calculate password hash and AD token using ZTE specification
        print(f"LD token length: {len(ld_token)}, RD token length: {len(rd_token)}")

        # If tokens are empty, we might not be connected to the modem network
        if not ld_token or not rd_token:
            print("Warning: Empty tokens received. Make sure you're connected to the modem's network.")
            print("Continuing with empty tokens for testing...")

        # Calculate password hash: SHA256(SHA256(password).upper() + LD.upper()).upper()
        password_hash = calculate_password_hash(PASSWORD, ld_token)
        print(f"Password hash: {password_hash}")

        # Calculate AD hash: MD5(MD5(crVersion + waInnerVersion) + RD).upper()
        ad_hash = calculate_ad_hash(cr_version, wa_inner_version, rd_token)
        print(f"AD hash: {ad_hash}")

        # 5. Send login request
        print("Sending login request...")
        login_payload = {
            "isTest": "false",
            "goformId": "LOGIN_MULTI_USER",
            "user": USERNAME,
            "password": password_hash,
            "AD": ad_hash
        }

        response = session.post(GOFORM_URL, data=login_payload)
        response.raise_for_status()

        if response.json().get("result") == "0":
            print("Login successful!")

            # Extract session cookie
            cookie_header = response.headers.get("Set-Cookie")
            if cookie_header and "stok=" in cookie_header:
                import re
                pattern = re.compile(r'stok="[^"]*"')
                match = pattern.search(cookie_header)
                if match:
                    session_cookie = match.group(0)
                    print(f"Got session cookie: {session_cookie}")
                    # Add cookie to session for subsequent requests
                    session.headers.update({"Cookie": session_cookie})
        else:
            print(f"Login failed. Response: {response.text}")
            print("Make sure you're connected to the modem's network and credentials are correct.")
            return

        # 6. Disconnect and Connect to trigger IP change
        print("Sending disconnect request...")
        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": ad_hash.lower()  # Use lowercase AD hash for commands
        }
        disconnect_response = session.post(GOFORM_URL, data=disconnect_payload)
        print(f"Disconnect response: {disconnect_response.text}")

        print("Sending connect request...")
        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": ad_hash.lower()  # Use lowercase AD hash for commands
        }
        connect_response = session.post(GOFORM_URL, data=connect_payload)
        print(f"Connect response: {connect_response.text}")

        print("IP change process completed successfully!")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Choose your method
    # Method 1: change_ip()
    # Method 2: change_ip_requests()
    change_ip_requests()