import requests
import hashlib
import re

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://***********"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "superonline"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest()

def get_md5(input_string):
    """Returns the MD5 hash of a string."""
    return hashlib.md5(input_string.encode()).hexdigest()

def calculate_password_hash(password, ld):
    """Calculate password hash as per ZTE modem specification."""
    prefix_hash = get_sha256(password).upper()
    return get_sha256(prefix_hash + ld.upper()).upper()

def calculate_ad_hash(cr_version, wa_inner_version, rd):
    """Calculate AD hash as per ZTE modem specification."""
    prefix_hash = get_md5(cr_version + wa_inner_version)
    return get_md5(prefix_hash + rd).upper()

def calculate_ad_hash_variations(cr_version, wa_inner_version, rd, username, password):
    """Try different AD hash calculation methods for different modem variants."""
    variations = []

    # Method 1: Standard ZTE method (cr_version + wa_inner_version)
    if cr_version and wa_inner_version:
        prefix1 = get_md5(cr_version + wa_inner_version)
        variations.append(("Standard", get_md5(prefix1 + rd).upper()))

    # Method 2: Only wa_inner_version (for modems with empty cr_version)
    if wa_inner_version:
        prefix2 = get_md5(wa_inner_version)
        variations.append(("WA only", get_md5(prefix2 + rd).upper()))

    # Method 3: Username + password based
    prefix3 = get_md5(username + password)
    variations.append(("User+Pass", get_md5(prefix3 + rd).upper()))

    # Method 4: Just RD token
    variations.append(("RD only", get_md5(rd).upper()))

    # Method 5: Empty prefix
    variations.append(("Empty prefix", get_md5("" + rd).upper()))

    return variations

def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    # Set proper headers to mimic browser behavior exactly
    session.headers.update({
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "tr",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Host": "***********",
        "Origin": f"{MODEM_URL}",
        "Referer": f"{MODEM_URL}/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"'
    })

    try:
        # 0. First, get the main page to establish initial session and get zsidn cookie
        print("Getting initial session...")
        main_response = session.get(f"{MODEM_URL}/")
        print(f"Initial session cookies: {session.cookies}")

        # 1. Get device version info (cr_version and wa_inner_version)
        print("Getting device version info...")
        import time
        timestamp = int(time.time() * 1000)
        version_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=Language%2Ccr_version%2Cwa_inner_version&multi_data=1&_={timestamp}"
        print(f"Version URL: {version_url}")
        response = session.get(version_url)
        response.raise_for_status()

        version_data = response.json()
        print(f"Version response: {version_data}")
        cr_version = version_data.get("cr_version", "")
        wa_inner_version = version_data.get("wa_inner_version", "")
        print(f"Got version info - cr_version: '{cr_version}', wa_inner_version: '{wa_inner_version}'")

        # 2. Get LD token
        print("Getting LD token...")
        timestamp += 1
        ld_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=LD&_={timestamp}"
        response = session.get(ld_url)
        response.raise_for_status()

        ld_data = response.json()
        if "LD" not in ld_data:
            print(f"Could not get LD token. Response: {response.text}")
            return

        ld_token = ld_data["LD"]
        print(f"Got LD token: {ld_token}")

        # 3. Get RD token
        print("Getting RD token...")
        timestamp += 1
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        if "RD" not in rd_data:
            print(f"Could not get RD token. Response: {response.text}")
            return

        rd_token = rd_data["RD"]
        print(f"Got RD token: {rd_token}")

        # 4. Calculate password hash and try different AD hash methods
        print(f"LD token length: {len(ld_token)}, RD token length: {len(rd_token)}")

        # If tokens are empty, we might not be connected to the modem network
        if not ld_token or not rd_token:
            print("Warning: Empty tokens received. Make sure you're connected to the modem's network.")
            print("Continuing with empty tokens for testing...")

        # Calculate password hash: SHA256(SHA256(password).upper() + LD.upper()).upper()
        password_hash = calculate_password_hash(PASSWORD, ld_token)
        print(f"Password hash: {password_hash}")

        # Try different AD hash calculation methods
        ad_variations = calculate_ad_hash_variations(cr_version, wa_inner_version, rd_token, USERNAME, PASSWORD)
        print(f"Trying {len(ad_variations)} different AD hash methods...")

        # 5. Try login with different AD hash methods
        login_successful = False
        successful_ad_hash = None

        for method_name, ad_hash in ad_variations:
            print(f"\nTrying {method_name} method - AD: {ad_hash[:16]}...")
            login_payload = {
                "isTest": "false",
                "goformId": "LOGIN_MULTI_USER",
                "user": USERNAME,
                "password": password_hash,
                "AD": ad_hash
            }

            response = session.post(GOFORM_URL, data=login_payload)
            response.raise_for_status()

            if response.json().get("result") == "0":
                print(f"✅ Login successful with {method_name} method!")
                successful_ad_hash = ad_hash
                login_successful = True

                # Extract session cookie
                cookie_header = response.headers.get("Set-Cookie")
                if cookie_header and "stok=" in cookie_header:
                    import re
                    pattern = re.compile(r'stok="[^"]*"')
                    match = pattern.search(cookie_header)
                    if match:
                        session_cookie = match.group(0)
                        print(f"Got session cookie: {session_cookie}")
                        # Add cookie to session for subsequent requests
                        session.headers.update({"Cookie": session_cookie})
                break
            else:
                print(f"❌ {method_name} method failed: {response.text}")

        if not login_successful:
            print("\n❌ All AD hash methods failed. Make sure you're connected to the modem's network.")
            return

        # 6. Check current connection status first
        print("Checking current connection status...")
        timestamp = int(time.time() * 1000)
        status_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=ppp_status%2Cwan_ipaddr%2Cnetwork_type%2Csignalbar&_={timestamp}"
        status_response = session.get(status_url)
        status_data = status_response.json()
        print(f"Current status: {status_data}")

        current_ppp_status = status_data.get("ppp_status", "unknown")
        print(f"Current PPP status: {current_ppp_status}")

        # 7. Disconnect and Connect to trigger IP change
        # Get fresh RD token for disconnect operation
        print("Getting fresh RD token for disconnect...")
        timestamp = int(time.time() * 1000)
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        rd_data = response.json()
        disconnect_rd_token = rd_data.get("RD", "")
        print(f"Disconnect RD token: {disconnect_rd_token}")

        # Calculate fresh AD hash for disconnect
        disconnect_ad_hash = calculate_ad_hash(cr_version, wa_inner_version, disconnect_rd_token)
        print(f"Disconnect AD hash: {disconnect_ad_hash}")

        print("Sending disconnect request...")
        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": disconnect_ad_hash
        }
        disconnect_response = session.post(GOFORM_URL, data=disconnect_payload)
        print(f"Disconnect response: {disconnect_response.text}")

        # Check if disconnect was successful by checking status
        time.sleep(3)
        print("Checking status after disconnect...")
        timestamp = int(time.time() * 1000)
        status_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=ppp_status%2Cwan_ipaddr%2Cnetwork_type&_={timestamp}"
        status_response = session.get(status_url)
        status_data = status_response.json()
        print(f"Status after disconnect: {status_data}")

        after_disconnect_ppp_status = status_data.get("ppp_status", "unknown")
        print(f"PPP status after disconnect: {after_disconnect_ppp_status}")

        # Check if status actually changed
        if current_ppp_status != after_disconnect_ppp_status:
            print(f"✅ Status changed from '{current_ppp_status}' to '{after_disconnect_ppp_status}' - disconnect may have worked!")
        else:
            print(f"⚠️  Status unchanged: '{current_ppp_status}' - disconnect may not have worked")

        # Wait a moment before reconnecting
        import time
        time.sleep(3)

        # Get fresh RD token for connect operation
        print("Getting fresh RD token for connect...")
        timestamp = int(time.time() * 1000)
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        rd_data = response.json()
        connect_rd_token = rd_data.get("RD", "")
        print(f"Connect RD token: {connect_rd_token}")

        # Calculate fresh AD hash for connect
        connect_ad_hash = calculate_ad_hash(cr_version, wa_inner_version, connect_rd_token)
        print(f"Connect AD hash: {connect_ad_hash}")

        print("Sending connect request...")
        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": connect_ad_hash
        }
        connect_response = session.post(GOFORM_URL, data=connect_payload)
        print(f"Connect response: {connect_response.text}")

        # Check if either succeeded or if they're expected to fail
        disconnect_result = disconnect_response.json().get("result", "unknown")
        connect_result = connect_response.json().get("result", "unknown")

        if disconnect_result == "0" or connect_result == "0":
            print("✅ IP change process completed successfully!")
        elif disconnect_result == "failure" and connect_result == "failure":
            print("⚠️  Both disconnect and connect returned 'failure'")
            print("   Trying alternative method: PPP disconnect/connect...")

            # Try alternative PPP-based disconnect/connect
            ppp_disconnect_payload = {
                "isTest": "false",
                "notCallback": "true",
                "goformId": "DISCONNECT_NETWORK",
                "ppp_status": "ppp_disconnected",
                "AD": successful_ad_hash.lower()
            }
            ppp_disconnect_response = session.post(GOFORM_URL, data=ppp_disconnect_payload)
            print(f"PPP Disconnect response: {ppp_disconnect_response.text}")

            time.sleep(2)

            ppp_connect_payload = {
                "isTest": "false",
                "notCallback": "true",
                "goformId": "CONNECT_NETWORK",
                "ppp_status": "ppp_connected",
                "AD": successful_ad_hash.lower()
            }
            ppp_connect_response = session.post(GOFORM_URL, data=ppp_connect_payload)
            print(f"PPP Connect response: {ppp_connect_response.text}")

            ppp_disconnect_result = ppp_disconnect_response.json().get("result", "unknown")
            ppp_connect_result = ppp_connect_response.json().get("result", "unknown")

            if ppp_disconnect_result == "0" or ppp_connect_result == "0":
                print("✅ IP change completed with alternative method!")
            else:
                print("⚠️  Alternative method also failed. Manual IP change may be required.")
                print("   Check your modem's web interface to manually disconnect/reconnect")
        else:
            print(f"⚠️  Unexpected responses - disconnect: {disconnect_result}, connect: {connect_result}")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Choose your method
    # Method 1: change_ip()
    # Method 2: change_ip_requests()
    change_ip_requests()