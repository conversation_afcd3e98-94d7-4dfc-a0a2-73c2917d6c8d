import requests
import hashlib
import time

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://***********"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "superonline"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest()


def calculate_password_hash(password, ld):
    """Calculate password hash as per ZTE modem specification."""
    prefix_hash = get_sha256(password).upper()
    return get_sha256(prefix_hash + ld.upper()).upper()

def calculate_ad_hash(cr_version, wa_inner_version, rd):
    """Calculate AD hash using SHA256 (based on HAR file analysis)."""
    prefix_hash = get_sha256(cr_version + wa_inner_version)
    return get_sha256(prefix_hash + rd).upper()



def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    # Set proper headers to mimic browser behavior exactly
    session.headers.update({
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "tr",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Host": "***********",
        "Origin": f"{MODEM_URL}",
        "Referer": f"{MODEM_URL}/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"'
    })

    try:
        # 1. Get device version info (cr_version and wa_inner_version)
        print("Getting device version info...")
        timestamp = int(time.time() * 1000)
        version_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=Language%2Ccr_version%2Cwa_inner_version&multi_data=1&_={timestamp}"
        response = session.get(version_url)
        response.raise_for_status()

        version_data = response.json()
        cr_version = version_data.get("cr_version", "")
        wa_inner_version = version_data.get("wa_inner_version", "")
        print(f"Got version info - cr_version: '{cr_version}', wa_inner_version: '{wa_inner_version}'")

        # 2. Get LD token
        print("Getting LD token...")
        timestamp += 1
        ld_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=LD&_={timestamp}"
        response = session.get(ld_url)
        response.raise_for_status()

        ld_data = response.json()
        ld_token = ld_data.get("LD", "")
        print(f"Got LD token: {ld_token}")

        # 3. Get RD token
        print("Getting RD token...")
        timestamp += 1
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        rd_token = rd_data.get("RD", "")
        print(f"Got RD token: {rd_token}")

        # 4. Calculate password hash and AD token
        if not ld_token or not rd_token:
            print("Warning: Empty tokens received. Make sure you're connected to the modem's network.")
            return

        password_hash = calculate_password_hash(PASSWORD, ld_token)
        ad_hash = calculate_ad_hash(cr_version, wa_inner_version, rd_token)
        print(f"Password hash: {password_hash}")
        print(f"AD hash: {ad_hash}")

        # 5. Send login request
        print("Sending login request...")
        login_payload = {
            "isTest": "false",
            "goformId": "LOGIN_MULTI_USER",
            "user": USERNAME,
            "password": password_hash,
            "AD": ad_hash
        }

        response = session.post(GOFORM_URL, data=login_payload)
        response.raise_for_status()

        if response.json().get("result") == "0":
            print("✅ Login successful!")

            # Check if we got the zsidn cookie
            zsidn_cookie = None
            for cookie in session.cookies:
                if cookie.name == 'zsidn':
                    zsidn_cookie = cookie.value
                    print(f"Got zsidn cookie: {zsidn_cookie}")
                    break

            if not zsidn_cookie:
                print("⚠️  Warning: No zsidn cookie received")
                return

            # Make post-login requests to establish full session state (exact browser timing)
            print("Establishing session state with realistic timing...")

            # Wait 160ms before first post-login request (as browser does)
            time.sleep(0.16)
            timestamp = int(time.time() * 1000)

            # First post-login request: user account info
            user_info_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&multi_data=1&cmd=user%2Cadmin_password_changed%2CFirewallDisable%2Cweb_current_account&_={timestamp}"
            response = session.get(user_info_url)
            print(f"User info response: {response.text}")

            # Wait 92ms before second request
            time.sleep(0.092)
            timestamp = int(time.time() * 1000)

            # Second post-login request: privacy flags
            privacy_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=privacy_read_flag%2Ctr069_user_improv_notify_flag&multi_data=1&_={timestamp}"
            response = session.get(privacy_url)
            print(f"Privacy flags response: {response.text}")

            # Wait 85ms before third request
            time.sleep(0.085)
            timestamp = int(time.time() * 1000)

            # Third post-login request: wifi password init flag
            wifi_init_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=web_wifi_password_init_flag&multi_data=1&_={timestamp}"
            response = session.get(wifi_init_url)
            print(f"WiFi init flag response: {response.text}")

            # Wait 82ms before fourth request
            time.sleep(0.082)
            timestamp = int(time.time() * 1000)

            # Fourth post-login request: SMS command status info
            sms_status_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=sms_cmd_status_info&sms_cmd=1&_={timestamp}"
            response = session.get(sms_status_url)
            print(f"SMS status response: {response.text}")

            # Wait remaining time to reach 6.146 seconds total from login (browser timing)
            # We've waited: 160 + 92 + 85 + 82 = 419ms, need 6146 - 419 = 5727ms more
            print("Waiting for optimal disconnect timing (5.7 seconds)...")
            time.sleep(5.727)
        else:
            print(f"Login failed. Response: {response.text}")
            return



        # 6. Disconnect network (fetch fresh RD token but use original AD hash)
        print("Getting fresh RD token for disconnect...")
        timestamp = int(time.time() * 1000)
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        disconnect_rd_token = rd_data.get("RD", "")
        print(f"Got fresh RD token: {disconnect_rd_token}")

        print("Sending disconnect request...")
        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": ad_hash  # Use original AD hash from login
        }
        disconnect_response = session.post(GOFORM_URL, data=disconnect_payload)
        print(f"Disconnect response: {disconnect_response.text}")
        disconnect_result = disconnect_response.json().get("result", "unknown")

        # 7. Wait and reconnect (fetch fresh RD token but use original AD hash)
        time.sleep(3)

        print("Getting fresh RD token for connect...")
        timestamp = int(time.time() * 1000)
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        connect_rd_token = rd_data.get("RD", "")
        print(f"Got fresh RD token: {connect_rd_token}")

        print("Sending connect request...")
        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": ad_hash  # Use original AD hash from login
        }
        connect_response = session.post(GOFORM_URL, data=connect_payload)
        print(f"Connect response: {connect_response.text}")

        # Check final results
        connect_result = connect_response.json().get("result", "unknown")

        if disconnect_result == "success" or connect_result == "success":
            print("✅ IP change process completed successfully!")
        else:
            print(f"⚠️  Disconnect: {disconnect_result}, Connect: {connect_result}")
            print("   Check your modem's web interface to verify if IP changed")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    change_ip_requests()
