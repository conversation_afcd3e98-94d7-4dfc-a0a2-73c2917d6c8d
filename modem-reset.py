import requests
import hashlib
import re

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://***********"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "superonline"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest().upper()

def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    try:
        # 1. Get LD token
        print("Getting LD token...")
        import time
        timestamp = int(time.time() * 1000)
        ld_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=LD&_={timestamp}"
        response = session.get(ld_url)
        response.raise_for_status()

        ld_data = response.json()
        if "LD" not in ld_data:
            print(f"Could not get LD token. Response: {response.text}")
            return

        ld_token = ld_data["LD"]
        print(f"Got LD token: {ld_token}")

        # 2. Get RD token
        print("Getting RD token...")
        timestamp += 1
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        if "RD" not in rd_data:
            print(f"Could not get RD token. Response: {response.text}")
            return

        rd_token = rd_data["RD"]
        print(f"Got RD token: {rd_token}")

        # 3. Calculate password hash and AD token
        # Based on your browser traffic, we need to figure out the hashing logic
        print(f"LD token length: {len(ld_token)}, RD token length: {len(rd_token)}")

        # If tokens are empty, we might not be connected to the modem network
        if not ld_token or not rd_token:
            print("Warning: Empty tokens received. Make sure you're connected to the modem's network.")
            print("Continuing with empty tokens for testing...")

        first_hash = get_sha256(PASSWORD)
        print(f"Password first hash: {first_hash}")

        # Try different combinations for the password field
        # Common patterns: password+rd, hash(password)+rd, hash(password+rd)
        password_combinations = [
            get_sha256(PASSWORD + rd_token),  # password + rd
            get_sha256(first_hash + rd_token),  # hash(password) + rd
            get_sha256(PASSWORD + ld_token),  # password + ld
            get_sha256(first_hash + ld_token),  # hash(password) + ld
        ]

        # Try different combinations for the AD field
        ad_combinations = [
            get_sha256(f"{USERNAME}{first_hash}{rd_token}"),  # user + hash(pass) + rd
            get_sha256(f"{USERNAME}{PASSWORD}{rd_token}"),    # user + pass + rd
            get_sha256(f"{first_hash}{rd_token}"),            # hash(pass) + rd
            get_sha256(f"{ld_token}{rd_token}"),              # ld + rd
            get_sha256(f"{USERNAME}{ld_token}{rd_token}"),    # user + ld + rd
        ]

        print("Trying different hash combinations...")
        for i, (pwd_hash, ad_hash) in enumerate(zip(password_combinations, ad_combinations)):
            print(f"Combination {i+1}: password={pwd_hash[:16]}..., AD={ad_hash[:16]}...")

        # 4. Try login with different hash combinations
        login_successful = False
        for i, (pwd_hash, ad_hash) in enumerate(zip(password_combinations, ad_combinations)):
            print(f"\nTrying combination {i+1}...")
            login_payload = {
                "isTest": "false",
                "goformId": "LOGIN_MULTI_USER",
                "user": USERNAME,
                "password": pwd_hash,
                "AD": ad_hash
            }

            response = session.post(GOFORM_URL, data=login_payload)
            response.raise_for_status()

            if response.json().get("result") == "0":
                print(f"Login successful with combination {i+1}!")
                login_successful = True
                break
            else:
                print(f"Combination {i+1} failed: {response.text}")

        if not login_successful:
            print("\nAll login combinations failed. The hashing logic might be different.")
            print("Please check the browser network tab for the exact hash values used.")
            return

        # 4. Disconnect and Connect
        # A new 'AD' token may be required for each command. This is a simplification.
        # Often, a new token is passed in the page after login. For simplicity, we reuse.

        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": ad_hash  # This might need to be a new, updated token
        }
        print("Sending disconnect request...")
        session.post(GOFORM_URL, data=disconnect_payload)

        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": ad_hash # This might need to be a new, updated token
        }
        print("Sending connect request...")
        session.post(GOFORM_URL, data=connect_payload)

        print("IP change process triggered.")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Choose your method
    # Method 1: change_ip()
    # Method 2: change_ip_requests()
    change_ip_requests()