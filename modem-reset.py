import requests
import hashlib
import re

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://***********"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "super"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest().upper()

def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    try:
        # 1. Get the login page to extract the random value ('rd')
        print("Fetching login page to get security token...")
        response = session.get(MODEM_URL)

        # Use regex to find the 'rd' value in the HTML/JS response
        # The pattern may vary. Inspect the page source to find it.
        match = re.search(r'var rd = "(\w+)"', response.text)
        if not match:
            print("Could not find the random security token ('rd') on the page.")
            return

        rd = match.group(1)
        print(f"Found token (rd): {rd}")

        # 2. Replicate the hashing logic (THIS IS THE CRITICAL PART)
        # This is a common pattern for ZTE, but may need adjustment.
        first_hash = get_sha256(PASSWORD)
        combined_hash = get_sha256(first_hash + rd)

        # The 'AD' token hash might use 'rd' as well or be a different combo
        # For this example, let's assume it's also a hash of user+pass+rd
        ad_hash = get_sha256(f"{USERNAME}{first_hash}{rd}")

        # 3. Send the login request
        login_payload = {
            "isTest": "false",
            "goformId": "LOGIN_MULTI_USER",
            "user": USERNAME,
            "password": combined_hash,
            "AD": ad_hash
        }

        print("Sending login request...")
        response = session.post(GOFORM_URL, data=login_payload)
        response.raise_for_status()

        if response.json().get("result") == "0":
            print("Login successful!")
        else:
            print(f"Login failed. Response: {response.text}")
            return

        # 4. Disconnect and Connect
        # A new 'AD' token may be required for each command. This is a simplification.
        # Often, a new token is passed in the page after login. For simplicity, we reuse.

        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": ad_hash  # This might need to be a new, updated token
        }
        print("Sending disconnect request...")
        session.post(GOFORM_URL, data=disconnect_payload)

        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": ad_hash # This might need to be a new, updated token
        }
        print("Sending connect request...")
        session.post(GOFORM_URL, data=connect_payload)

        print("IP change process triggered.")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Choose your method
    # Method 1: change_ip()
    # Method 2: change_ip_requests()
    change_ip_requests()