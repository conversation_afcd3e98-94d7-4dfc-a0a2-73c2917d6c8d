import requests
import hashlib
import time

# Disable warnings for self-signed certificates
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

MODEM_URL = "https://***********"
GOFORM_URL = f"{MODEM_URL}/goform/goform_set_cmd_process"
USERNAME = "admin"
PASSWORD = "superonline"

def get_sha256(input_string):
    """Returns the SHA256 hash of a string."""
    return hashlib.sha256(input_string.encode()).hexdigest()


def calculate_password_hash(password, ld):
    """Calculate password hash as per ZTE modem specification."""
    prefix_hash = get_sha256(password).upper()
    return get_sha256(prefix_hash + ld.upper()).upper()

def calculate_ad_hash(cr_version, wa_inner_version, rd):
    """Calculate AD hash using SHA256 (based on HAR file analysis)."""
    prefix_hash = get_sha256(cr_version + wa_inner_version)
    return get_sha256(prefix_hash + rd).upper()



def change_ip_requests():
    session = requests.Session()
    session.verify = False # Ignore SSL certificate errors

    # Set proper headers to mimic browser behavior exactly
    session.headers.update({
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "tr",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Host": "***********",
        "Origin": f"{MODEM_URL}",
        "Referer": f"{MODEM_URL}/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"'
    })

    try:
        # 1. Get device version info (cr_version and wa_inner_version)
        print("Getting device version info...")
        timestamp = int(time.time() * 1000)
        version_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=Language%2Ccr_version%2Cwa_inner_version&multi_data=1&_={timestamp}"
        response = session.get(version_url)
        response.raise_for_status()

        version_data = response.json()
        cr_version = version_data.get("cr_version", "")
        wa_inner_version = version_data.get("wa_inner_version", "")
        print(f"Got version info - cr_version: '{cr_version}', wa_inner_version: '{wa_inner_version}'")

        # 2. Get LD token
        print("Getting LD token...")
        timestamp += 1
        ld_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=LD&_={timestamp}"
        response = session.get(ld_url)
        response.raise_for_status()

        ld_data = response.json()
        ld_token = ld_data.get("LD", "")
        print(f"Got LD token: {ld_token}")

        # 3. Get RD token
        print("Getting RD token...")
        timestamp += 1
        rd_url = f"{MODEM_URL}/goform/goform_get_cmd_process?isTest=false&cmd=RD&_={timestamp}"
        response = session.get(rd_url)
        response.raise_for_status()

        rd_data = response.json()
        rd_token = rd_data.get("RD", "")
        print(f"Got RD token: {rd_token}")

        # 4. Calculate password hash and AD token
        if not ld_token or not rd_token:
            print("Warning: Empty tokens received. Make sure you're connected to the modem's network.")
            return

        password_hash = calculate_password_hash(PASSWORD, ld_token)

        # Try different AD hash calculations to match browser behavior
        ad_hash_method1 = calculate_ad_hash(cr_version, wa_inner_version, rd_token)
        ad_hash_method2 = rd_token.upper()  # Maybe it's just the RD token
        ad_hash_method3 = get_sha256(rd_token).upper()  # Maybe it's SHA256 of RD token

        print(f"Password hash: {password_hash}")
        print(f"AD hash (method1 - cr+wa+rd): {ad_hash_method1}")
        print(f"AD hash (method2 - rd only): {ad_hash_method2}")
        print(f"AD hash (method3 - sha256(rd)): {ad_hash_method3}")

        # Try each method for login
        ad_methods = [
            ("method1", ad_hash_method1),
            ("method2", ad_hash_method2),
            ("method3", ad_hash_method3)
        ]

        successful_ad_hash = None
        for method_name, ad_hash in ad_methods:
            print(f"\nTrying login with {method_name}...")
            login_payload = {
                "isTest": "false",
                "goformId": "LOGIN_MULTI_USER",
                "user": USERNAME,
                "password": password_hash,
                "AD": ad_hash
            }

            response = session.post(GOFORM_URL, data=login_payload)
            response.raise_for_status()

            if response.json().get("result") == "0":
                print(f"✅ Login successful with {method_name}!")
                successful_ad_hash = ad_hash

                # Check if we got the zsidn cookie
                zsidn_cookie = None
                for cookie in session.cookies:
                    if cookie.name == 'zsidn':
                        zsidn_cookie = cookie.value
                        print(f"Got zsidn cookie: {zsidn_cookie}")
                        break

                if not zsidn_cookie:
                    print("⚠️  Warning: No zsidn cookie received")
                    return
                break
            else:
                print(f"❌ Login failed with {method_name}: {response.text}")

        if not successful_ad_hash:
            print("❌ All login methods failed")
            return



        # 5. Disconnect network (using the same AD hash from login)
        print(f"Using successful AD hash for disconnect: {successful_ad_hash}")
        print("Sending disconnect request...")
        disconnect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "DISCONNECT_NETWORK",
            "AD": successful_ad_hash  # Use the same AD hash from successful login
        }
        disconnect_response = session.post(GOFORM_URL, data=disconnect_payload)
        print(f"Disconnect response: {disconnect_response.text}")
        disconnect_result = disconnect_response.json().get("result", "unknown")

        # 6. Wait and reconnect (using the same AD hash from login)
        time.sleep(3)

        print(f"Using successful AD hash for connect: {successful_ad_hash}")
        print("Sending connect request...")
        connect_payload = {
            "isTest": "false",
            "notCallback": "true",
            "goformId": "CONNECT_NETWORK",
            "AD": successful_ad_hash  # Use the same AD hash from successful login
        }
        connect_response = session.post(GOFORM_URL, data=connect_payload)
        print(f"Connect response: {connect_response.text}")

        # Check final results
        connect_result = connect_response.json().get("result", "unknown")

        if disconnect_result == "success" or connect_result == "success":
            print("✅ IP change process completed successfully!")
        else:
            print(f"⚠️  Disconnect: {disconnect_result}, Connect: {connect_result}")
            print("   Check your modem's web interface to verify if IP changed")

    except requests.exceptions.RequestException as e:
        print(f"An HTTP error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    change_ip_requests()
